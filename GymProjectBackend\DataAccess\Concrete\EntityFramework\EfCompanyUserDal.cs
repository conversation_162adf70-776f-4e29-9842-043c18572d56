﻿
using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyUserDal : EfEntityRepositoryBase<CompanyUser, GymContext>, ICompanyUserDal
    {
        public List<CompanyUserDetailDto> GetCompanyUserDetails()
        {
            using (GymContext context = new GymContext())
            {
                var result = from c in context.CompanyUsers
                             join ci in context.Cities
                             on c.CityID equals ci.CityID
                             join t in context.Towns
                             on c.TownID equals t.TownID
                           where c.IsActive == true
                             select new CompanyUserDetailDto
                             {
                                 CompanyUserId = c.CompanyUserID,
                                 CompanyUserName = c.Name,
                                 CityName = ci.CityName,
                                 TownName = t.TownName,
                                 CompanyUserPhoneNumber = c.PhoneNumber,
                                 CompanyUserEmail = c.Email
                             };
                return result.ToList();
            }
        }
        public List<CompanyDetailDto> GetCompanyDetails()
        {
            using (GymContext context = new GymContext())
            {
                var result = from c in context.CompanyUsers
                             join b in context.UserCompanies
                            on c.CompanyUserID equals b.UserID
                             join x in context.Cities
                             on c.CityID equals x.CityID
                             join t in context.Towns
                             on c.TownID equals t.TownID
                             join k in context.Companies
                             on b.CompanyId equals k.CompanyID
                             join f in context.CompanyAdresses
                             on k.CompanyID equals f.CompanyID
                             select new CompanyDetailDto
                             {
                                 CompanyUserId = c.CompanyUserID,
                                 CompanyUserName = c.Name,
                                 CityName = x.CityName,
                                 TownName = t.TownName,
                                 CompanyUserPhoneNumber = c.PhoneNumber,
                                 CompanyPhoneNumber = k.PhoneNumber,
                                 CompanyUserEmail = c.Email,
                                 CompanyAdress = f.Adress,
                                 CompanyName = k.CompanyName,
                                 CompanyAdressId = f.CompanyAdressID,
                                 CompanyId = k.CompanyID,
                                 UserCompanyId = b.UserCompanyID,
                                 IsActive = b.IsActive

                             }; 

                return result.ToList();
            }

        }
        public List<CompanyDetailDto> GetCompanyUserDetailsByCityId(int cityId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from c in context.CompanyUsers
                             join b in context.UserCompanies on c.CompanyUserID equals b.UserID
                             join x in context.Cities on c.CityID equals x.CityID
                             join t in context.Towns on c.TownID equals t.TownID
                             join k in context.Companies on b.CompanyId equals k.CompanyID
                             join f in context.CompanyAdresses on k.CompanyID equals f.CompanyID
                             where c.CityID == cityId
                             select new CompanyDetailDto
                             {
                                 CompanyUserId = c.CompanyUserID,
                                 CompanyUserName = c.Name,
                                 CityName = x.CityName,
                                 TownName = t.TownName,
                                 CompanyUserPhoneNumber = c.PhoneNumber,
                                 CompanyPhoneNumber = k.PhoneNumber,
                                 CompanyUserEmail = c.Email,
                                 CompanyAdress = f.Adress,
                                 CompanyName = k.CompanyName
                             };

                return result.ToList();
            }

        }
    }
}