﻿
using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyUserDal : EfEntityRepositoryBase<CompanyUser, GymContext>, ICompanyUserDal
    {
        public List<CompanyUserDetailDto> GetCompanyUserDetails()
        {
            using (GymContext context = new GymContext())
            {
                var result = from c in context.CompanyUsers
                             join ci in context.Cities
                             on c.CityID equals ci.CityID
                             join t in context.Towns
                             on c.TownID equals t.TownID
                           where c.IsActive == true
                             select new CompanyUserDetailDto
                             {
                                 CompanyUserId = c.CompanyUserID,
                                 CompanyUserName = c.Name,
                                 CityName = ci.CityName,
                                 TownName = t.TownName,
                                 CompanyUserPhoneNumber = c.PhoneNumber,
                                 CompanyUserEmail = c.Email
                             };
                return result.ToList();
            }
        }
        public List<CompanyDetailDto> GetCompanyDetails()
        {
            using (GymContext context = new GymContext())
            {
                var result = from c in context.CompanyUsers
                             join b in context.UserCompanies
                            on c.CompanyUserID equals b.UserID
                             join x in context.Cities
                             on c.CityID equals x.CityID
                             join t in context.Towns
                             on c.TownID equals t.TownID
                             join k in context.Companies
                             on b.CompanyId equals k.CompanyID
                             join f in context.CompanyAdresses
                             on k.CompanyID equals f.CompanyID
                             select new CompanyDetailDto
                             {
                                 CompanyUserId = c.CompanyUserID,
                                 CompanyUserName = c.Name,
                                 CityName = x.CityName,
                                 TownName = t.TownName,
                                 CompanyUserPhoneNumber = c.PhoneNumber,
                                 CompanyPhoneNumber = k.PhoneNumber,
                                 CompanyUserEmail = c.Email,
                                 CompanyAdress = f.Adress,
                                 CompanyName = k.CompanyName,
                                 CompanyAdressId = f.CompanyAdressID,
                                 CompanyId = k.CompanyID,
                                 UserCompanyId = b.UserCompanyID,
                                 IsActive = b.IsActive

                             }; 

                return result.ToList();
            }

        }
        public List<CompanyDetailDto> GetCompanyUserDetailsByCityId(int cityId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from c in context.CompanyUsers
                             join b in context.UserCompanies on c.CompanyUserID equals b.UserID
                             join x in context.Cities on c.CityID equals x.CityID
                             join t in context.Towns on c.TownID equals t.TownID
                             join k in context.Companies on b.CompanyId equals k.CompanyID
                             join f in context.CompanyAdresses on k.CompanyID equals f.CompanyID
                             where c.CityID == cityId
                             select new CompanyDetailDto
                             {
                                 CompanyUserId = c.CompanyUserID,
                                 CompanyUserName = c.Name,
                                 CityName = x.CityName,
                                 TownName = t.TownName,
                                 CompanyUserPhoneNumber = c.PhoneNumber,
                                 CompanyPhoneNumber = k.PhoneNumber,
                                 CompanyUserEmail = c.Email,
                                 CompanyAdress = f.Adress,
                                 CompanyName = k.CompanyName
                             };

                return result.ToList();
            }
        }

        // Yeni eklenen metodlar
        public CompanyUserFullDetailDto GetCompanyUserFullDetails(int companyUserID)
        {
            using (GymContext context = new GymContext())
            {
                var result = from cu in context.CompanyUsers
                             join c in context.Cities on cu.CityID equals c.CityID
                             join t in context.Towns on cu.TownID equals t.TownID
                             join uc in context.UserCompanies on cu.CompanyUserID equals uc.UserID into ucGroup
                             from uc in ucGroup.DefaultIfEmpty()
                             join comp in context.Companies on uc.CompanyId equals comp.CompanyID into compGroup
                             from comp in compGroup.DefaultIfEmpty()
                             join u in context.Users on cu.Email equals u.Email into userGroup
                             from u in userGroup.DefaultIfEmpty()
                             where cu.CompanyUserID == companyUserID
                             select new CompanyUserFullDetailDto
                             {
                                 // CompanyUser bilgileri
                                 CompanyUserID = cu.CompanyUserID,
                                 Name = cu.Name,
                                 PhoneNumber = cu.PhoneNumber,
                                 Email = cu.Email,
                                 CityID = cu.CityID,
                                 TownID = cu.TownID,
                                 CityName = c.CityName,
                                 TownName = t.TownName,
                                 IsActive = cu.IsActive,
                                 CreationDate = cu.CreationDate,
                                 UpdatedDate = cu.UpdatedDate,

                                 // User bilgileri
                                 UserID = u.UserID,
                                 FirstName = u.FirstName,
                                 LastName = u.LastName,
                                 UserIsActive = u.IsActive,
                                 RequirePasswordChange = u.RequirePasswordChange,

                                 // Company bilgileri
                                 CompanyID = comp.CompanyID,
                                 CompanyName = comp.CompanyName,
                                 CompanyPhone = comp.PhoneNumber,
                                 CompanyIsActive = comp.IsActive,

                                 // İstatistik bilgileri (şimdilik default değerler)
                                 TotalMembers = 0,
                                 ActiveMembers = 0,
                                 MonthlyRevenue = 0
                             };

                return result.FirstOrDefault();
            }
        }

        public PaginatedCompanyUserDto GetCompanyUsersPaginated(int pageNumber, int pageSize, string searchTerm = "")
        {
            using (GymContext context = new GymContext())
            {
                var query = from cu in context.CompanyUsers
                           join c in context.Cities on cu.CityID equals c.CityID
                           join t in context.Towns on cu.TownID equals t.TownID
                           join uc in context.UserCompanies on cu.CompanyUserID equals uc.UserID into ucGroup
                           from uc in ucGroup.DefaultIfEmpty()
                           join comp in context.Companies on uc.CompanyId equals comp.CompanyID into compGroup
                           from comp in compGroup.DefaultIfEmpty()
                           join u in context.Users on cu.Email equals u.Email into userGroup
                           from u in userGroup.DefaultIfEmpty()
                           where cu.IsActive == true
                           select new CompanyUserFullDetailDto
                           {
                               CompanyUserID = cu.CompanyUserID,
                               Name = cu.Name,
                               PhoneNumber = cu.PhoneNumber,
                               Email = cu.Email,
                               CityID = cu.CityID,
                               TownID = cu.TownID,
                               CityName = c.CityName,
                               TownName = t.TownName,
                               IsActive = cu.IsActive,
                               CreationDate = cu.CreationDate,
                               UpdatedDate = cu.UpdatedDate,
                               UserID = u.UserID,
                               FirstName = u.FirstName,
                               LastName = u.LastName,
                               UserIsActive = u.IsActive,
                               RequirePasswordChange = u.RequirePasswordChange,
                               CompanyID = comp.CompanyID,
                               CompanyName = comp.CompanyName,
                               CompanyPhone = comp.PhoneNumber,
                               CompanyIsActive = comp.IsActive,
                               TotalMembers = 0,
                               ActiveMembers = 0,
                               MonthlyRevenue = 0
                           };

                // Arama filtresi uygula
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    searchTerm = searchTerm.ToLower();
                    query = query.Where(x =>
                        x.Name.ToLower().Contains(searchTerm) ||
                        x.Email.ToLower().Contains(searchTerm) ||
                        x.PhoneNumber.Contains(searchTerm) ||
                        x.CityName.ToLower().Contains(searchTerm) ||
                        x.TownName.ToLower().Contains(searchTerm) ||
                        (x.CompanyName != null && x.CompanyName.ToLower().Contains(searchTerm))
                    );
                }

                // Toplam kayıt sayısını al
                int totalCount = query.Count();

                // Sayfalama uygula
                var data = query
                    .OrderBy(x => x.Name)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PaginatedCompanyUserDto(data, totalCount, pageNumber, pageSize);
            }
        }
    }
}