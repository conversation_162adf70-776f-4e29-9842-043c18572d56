using Core.Entities;

namespace Entities.DTOs
{
    /// <summary>
    /// CompanyUser'ın tam güncelleme işlemi için DTO
    /// Çoklu tablo güncellemesi destekler
    /// </summary>
    public class CompanyUserFullUpdateDto : IDto
    {
        // CompanyUser güncellemeleri
        public int CompanyUserID { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public int CityID { get; set; }
        public int TownID { get; set; }
        public bool? IsActive { get; set; }
        
        // Company güncellemeleri (opsiyonel)
        public int? CompanyID { get; set; }
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        public string CompanyAddress { get; set; }
        public bool? CompanyIsActive { get; set; }
        
        // Kontrol alanları
        public bool EmailChanged { get; set; }
        public bool NameChanged { get; set; }
        public bool CompanyDataChanged { get; set; }
        
        // <PERSON><PERSON> (değişiklik kontrolü için)
        public string OldEmail { get; set; }
        public string OldName { get; set; }
    }
}
