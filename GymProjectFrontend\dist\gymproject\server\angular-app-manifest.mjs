
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: '96d8d00e26b798c187a2db8c8dc20c1546b19053c82e7f886733ad07e515b40a', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: 'aa4aabb9811cd4f0f5676d071024bd3939aadeba0e590daa0e5338dffe18e523', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-TBXNKKXU.css': {size: 298105, hash: '7i4JWBKmeRM', text: () => import('./assets-chunks/styles-TBXNKKXU_css.mjs').then(m => m.default)}
  },
};
