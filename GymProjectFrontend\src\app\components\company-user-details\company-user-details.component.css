/* Company User Details Component Styles */

/* CSS Variables */
:root {
  --primary-color: #4361ee;
  --secondary-color: #3a0ca3;
  --text-muted: #6c757d;
  --border-color: #dee2e6;
  --input-bg: #ffffff;
  --input-text: #495057;
  --primary: #4361ee;
  --primary-light: rgba(67, 97, 238, 0.1);
  --primary-rgb: 67, 97, 238;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Avatar Styles */
.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.avatar-circle-lg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* Status Badge */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

/* Filter Tags */
.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  background-color: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.remove-tag {
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.remove-tag:hover {
  opacity: 0.7;
}

/* Dark mode support */
[data-theme="dark"] .status-badge.active {
  background-color: #1e7e34;
  color: #d4edda;
}

[data-theme="dark"] .status-badge.inactive {
  background-color: #bd2130;
  color: #f8d7da;
}

[data-theme="dark"] .filter-tag {
  background-color: var(--primary-color);
  color: white;
}

/* Button Styles */
.btn-modern {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-modern-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-modern-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-modern-primary:hover {
  background-color: var(--secondary-color);
}

.btn-modern-success {
  background-color: #28a745;
  color: white;
}

.btn-modern-success:hover {
  background-color: #218838;
}

.btn-modern-danger {
  background-color: #dc3545;
  color: white;
}

.btn-modern-danger:hover {
  background-color: #c82333;
}

.btn-modern-info {
  background-color: #17a2b8;
  color: white;
}

.btn-modern-info:hover {
  background-color: #138496;
}

.btn-modern-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

.btn-modern-icon-sm {
  width: 30px;
  height: 30px;
  font-size: 0.875rem;
}

.btn-modern-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-modern-outline:hover, .btn-modern-outline.active {
  background-color: var(--primary-color);
  color: white;
}

.btn-group {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
}

.btn-group .btn-modern-outline {
  border-radius: 0;
  margin: 0;
  border-right-width: 0;
}

.btn-group .btn-modern-outline:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.btn-group .btn-modern-outline:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-right-width: 1px;
}

/* Search Input */
.search-input-container {
  position: relative;
  margin-bottom: 1rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
  outline: none;
}

/* Dark mode search input */
[data-theme="dark"] .search-input {
  background-color: #4a5568;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .search-input:focus {
  background-color: #4a5568;
  border-color: #63b3ed;
  color: #e2e8f0;
}

/* Modern Badge */
.total-members-badge {
  display: flex;
  align-items: center;
}

.total-members-badge .modern-badge {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 50rem;
  display: inline-flex;
  align-items: center;
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  transition: all 0.3s ease;
}

.total-members-badge .modern-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
}

.total-members-badge .modern-badge i {
  font-size: 0.875rem;
}

.modern-badge-primary {
  background-color: rgba(67, 97, 238, 0.1);
  color: #4361ee;
  border-color: rgba(67, 97, 238, 0.2);
}

/* Dark mode modern badge */
[data-theme="dark"] .total-members-badge .modern-badge {
  background-color: var(--primary-light);
  color: var(--primary);
  border-color: rgba(var(--primary-rgb), 0.3);
}

[data-theme="dark"] .total-members-badge .modern-badge:hover {
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}