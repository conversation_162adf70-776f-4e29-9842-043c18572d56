/* Company User Details Component Styles */

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Avatar Styles */
.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.avatar-circle-lg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* Status Badge */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

/* Filter Tags */
.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  background-color: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.remove-tag {
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.remove-tag:hover {
  opacity: 0.7;
}

/* Dark mode support */
[data-theme="dark"] .status-badge.active {
  background-color: #1e7e34;
  color: #d4edda;
}

[data-theme="dark"] .status-badge.inactive {
  background-color: #bd2130;
  color: #f8d7da;
}

[data-theme="dark"] .filter-tag {
  background-color: var(--primary-color);
  color: white;
}