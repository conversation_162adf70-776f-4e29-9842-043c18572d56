/* Company User Details Component Styles */

/* CSS Variables */
:root {
  --primary-color: #4361ee;
  --secondary-color: #3a0ca3;
  --text-muted: #6c757d;
  --border-color: #dee2e6;
  --input-bg: #ffffff;
  --input-text: #495057;
  --primary: #4361ee;
  --primary-light: rgba(67, 97, 238, 0.1);
  --primary-rgb: 67, 97, 238;
}

/* Dark Mode CSS Variables */
[data-theme="dark"] {
  --primary-color: #4361ee;
  --secondary-color: #3a0ca3;
  --text-muted: #a0aec0;
  --border-color: #4a5568;
  --input-bg: #4a5568;
  --input-text: #e2e8f0;
  --primary: #4361ee;
  --primary-light: rgba(67, 97, 238, 0.2);
  --primary-rgb: 67, 97, 238;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Avatar Styles */
.avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.avatar-circle-lg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* Status Badge */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

.status-badge.active {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-badge.inactive {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Filter Tags */
.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  background-color: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.remove-tag {
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.remove-tag:hover {
  opacity: 0.7;
}

/* Dark mode support */
[data-theme="dark"] .status-badge.active {
  background-color: rgba(40, 167, 69, 0.2);
  color: #68d391;
  border-color: rgba(40, 167, 69, 0.3);
}

[data-theme="dark"] .status-badge.inactive {
  background-color: rgba(220, 53, 69, 0.2);
  color: #fc8181;
  border-color: rgba(220, 53, 69, 0.3);
}

[data-theme="dark"] .filter-tag {
  background-color: var(--primary-color);
  color: white;
}

/* Button Styles */
.btn-modern {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-modern-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-modern-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-modern-primary:hover {
  background-color: var(--secondary-color);
}

.btn-modern-success {
  background-color: #28a745;
  color: white;
}

.btn-modern-success:hover {
  background-color: #218838;
}

.btn-modern-danger {
  background-color: #dc3545;
  color: white;
}

.btn-modern-danger:hover {
  background-color: #c82333;
}

.btn-modern-info {
  background-color: #17a2b8;
  color: white;
}

.btn-modern-info:hover {
  background-color: #138496;
}

.btn-modern-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

.btn-modern-icon-sm {
  width: 30px;
  height: 30px;
  font-size: 0.875rem;
}

.btn-modern-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-modern-outline:hover, .btn-modern-outline.active {
  background-color: var(--primary-color);
  color: white;
}

.btn-group {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
}

.btn-group .btn-modern-outline {
  border-radius: 0;
  margin: 0;
  border-right-width: 0;
}

.btn-group .btn-modern-outline:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.btn-group .btn-modern-outline:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-right-width: 1px;
}

/* Search Input */
.search-input-container {
  position: relative;
  margin-bottom: 1rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
  outline: none;
}

/* Dark mode search input */
[data-theme="dark"] .search-input {
  background-color: #4a5568;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .search-input:focus {
  background-color: #4a5568;
  border-color: #63b3ed;
  color: #e2e8f0;
}

/* Modern Badge */
.total-members-badge {
  display: flex;
  align-items: center;
}

.total-members-badge .modern-badge {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 50rem;
  display: inline-flex;
  align-items: center;
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  transition: all 0.3s ease;
}

.total-members-badge .modern-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
}

.total-members-badge .modern-badge i {
  font-size: 0.875rem;
}

.modern-badge-primary {
  background-color: rgba(67, 97, 238, 0.1);
  color: #4361ee;
  border-color: rgba(67, 97, 238, 0.2);
}

/* Dark mode modern badge */
[data-theme="dark"] .total-members-badge .modern-badge {
  background-color: var(--primary-light);
  color: var(--primary);
  border-color: rgba(var(--primary-rgb), 0.3);
}

[data-theme="dark"] .total-members-badge .modern-badge:hover {
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}

/* Modern Card */
.modern-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: none;
  transition: all 0.3s ease;
}

.modern-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modern-card .card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-card .card-header h5 {
  margin: 0;
  font-weight: 600;
  color: #2d3748;
}

.modern-card .card-body {
  padding: 1.5rem;
}

/* Dark Mode Support */
[data-theme="dark"] .modern-card {
  background-color: #2d3748;
}

[data-theme="dark"] .modern-card .card-header {
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .modern-card .card-header h5 {
  color: #e2e8f0;
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0;
}

.modern-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 1rem;
  border-bottom: 2px solid #dee2e6;
  text-align: left;
}

.modern-table td {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.modern-table tbody tr {
  transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

/* Dark Mode Table */
[data-theme="dark"] .modern-table {
  background-color: #2d3748;
}

[data-theme="dark"] .modern-table th {
  background-color: #4a5568;
  color: #e2e8f0;
  border-bottom-color: #2d3748;
}

[data-theme="dark"] .modern-table td {
  border-bottom-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-table tbody tr {
  background-color: #2d3748;
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Dark mode avatar */
[data-theme="dark"] .avatar-circle {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .profile-image {
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* Button Styles */
.btn-modern {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-modern-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-modern-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-modern-outline:hover, .btn-modern-outline.active {
  background-color: var(--primary-color);
  color: white;
}

.btn-group {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
}

.btn-group .btn-modern-outline {
  border-radius: 0;
  margin: 0;
  border-right-width: 0;
}

.btn-group .btn-modern-outline:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.btn-group .btn-modern-outline:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-right-width: 1px;
}

.btn-modern-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-modern-primary:hover {
  background-color: var(--secondary-color);
}

.btn-modern-success {
  background-color: #28a745;
  color: white;
}

.btn-modern-success:hover {
  background-color: #218838;
}

.btn-modern-danger {
  background-color: #dc3545;
  color: white;
}

.btn-modern-danger:hover {
  background-color: #c82333;
}

.btn-modern-info {
  background-color: #17a2b8;
  color: white;
}

.btn-modern-info:hover {
  background-color: #138496;
}

.btn-modern-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

.btn-modern-icon-sm {
  width: 30px;
  height: 30px;
  font-size: 0.875rem;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .avatar-circle {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }

  .modern-card .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .modern-card .card-header .d-flex {
    margin-top: 1rem;
    width: 100%;
    flex-direction: column;
    gap: 1rem;
  }

  .btn-modern-sm {
    width: 100%;
  }
}

/* Pagination Styles */
.pagination {
  margin-bottom: 0;
}

.page-link {
  background-color: #ffffff;
  border-color: #dee2e6;
  color: #495057;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  margin: 0 0.125rem;
  transition: all 0.3s ease;
}

.page-link:hover {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #495057;
}

.page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.page-item.disabled .page-link {
  background-color: #ffffff;
  border-color: #dee2e6;
  color: #6c757d;
}

/* Dark Mode Pagination */
[data-theme="dark"] .page-link {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .page-link:hover {
  background-color: #4a5568;
}

[data-theme="dark"] .page-item.disabled .page-link {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #718096;
}

/* Dark Mode Button Styles */
[data-theme="dark"] .btn-modern-outline {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: transparent;
}

[data-theme="dark"] .btn-modern-outline:hover,
[data-theme="dark"] .btn-modern-outline.active {
  background-color: var(--primary-color);
  color: white;
}

/* Dark Mode Filter Tags */
[data-theme="dark"] .filter-tag {
  background-color: rgba(67, 97, 238, 0.3);
  color: #e2e8f0;
  border-color: rgba(67, 97, 238, 0.4);
}