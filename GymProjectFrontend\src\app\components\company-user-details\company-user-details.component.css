/* Company User Details Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Avatar Styles */
.avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.avatar-circle-lg {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* User Info Styles */
.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.user-meta {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Contact Info Styles */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

/* Location Info Styles */
.location-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.location-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

/* Company Info Styles */
.company-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.company-name {
  font-weight: 600;
  color: var(--text-primary);
}

.company-phone {
  font-size: 0.875rem;
}

/* Status Badges */
.status-active {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.status-inactive {
  background-color: var(--danger-light);
  color: var(--danger-dark);
}

.status-user-inactive {
  background-color: var(--warning-light);
  color: var(--warning-dark);
}

.status-password-required {
  background-color: var(--info-light);
  color: var(--info-dark);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.btn-action {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-action-info {
  background-color: var(--info-light);
  color: var(--info-dark);
}

.btn-action-info:hover {
  background-color: var(--info);
  color: white;
  transform: scale(1.1);
}

/* Card View Styles */
.cards-container {
  margin-top: 1rem;
}

.modern-user-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.modern-user-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.user-card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.user-card-info {
  flex: 1;
}

.user-card-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.user-card-email {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.user-card-status {
  margin-left: auto;
}

.user-card-body {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.user-card-detail {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.user-card-detail i {
  width: 16px;
  text-align: center;
}

/* Search and Filters */
.search-filters-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.modern-search-box {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  z-index: 2;
}

.modern-search-input {
  padding-left: 3rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--input-bg);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.modern-search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
  background: var(--input-focus-bg);
}

.results-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Total Users Badge */
.total-users-badge {
  display: flex;
  align-items: center;
}

/* Table Hover Effect */
.table-row-hover:hover {
  background-color: var(--hover-bg);
  transform: scale(1.01);
  transition: all 0.2s ease;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.empty-state-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.empty-state h5 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .contact-info,
  .location-info {
    gap: 0.25rem;
  }

  .action-buttons {
    flex-direction: column;
  }

  .modern-user-card {
    padding: 1rem;
  }

  .user-card-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .search-filters-section {
    padding: 1rem;
  }
}

/* Dark Mode Compatibility */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .modern-user-card:hover {
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
}