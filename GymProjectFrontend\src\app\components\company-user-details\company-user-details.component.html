<!-- Loading Overlay -->
<div class="loading-overlay" *ngIf="isLoading">
  <div class="spinner-container">
    <div class="modern-spinner"></div>
    <p class="mt-3">Şirket kullanıcıları yükleniyor...</p>
  </div>
</div>

<div class="container-fluid modern-container">
  <div class="row" [class.content-blur]="isLoading">
    <!-- Main Content -->
    <div class="col-md-12">
      <div class="modern-card">
        <div class="card-header">
          <h5>Şirket Kullanıcıları</h5>
          <div class="d-flex align-items-center gap-3">
            <!-- <PERSON>lam Kullanıcı Sayısı -->
            <div class="total-users-badge">
              <span class="modern-badge modern-badge-primary">
                <i class="fas fa-building me-2"></i>
                Toplam: {{ totalCompanyUsers }} Kullanıcı
              </span>
            </div>
            <div class="d-flex gap-2">
              <button
                class="btn-modern btn-modern-secondary"
                (click)="refreshData()"
                [disabled]="isLoading"
              >
                <i class="fas fa-sync-alt" [class.fa-spin]="isLoading"></i>
                Yenile
              </button>
              <button
                class="btn-modern btn-modern-outline-secondary"
                (click)="toggleViewMode()"
              >
                <i class="fas" [ngClass]="viewMode === 'table' ? 'fa-th-large' : 'fa-table'"></i>
                {{ viewMode === 'table' ? 'Kart Görünümü' : 'Tablo Görünümü' }}
              </button>
            </div>
          </div>
        </div>

        <div class="card-body">
          <!-- Search and Filters -->
          <div class="search-filters-section">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="modern-search-box">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    type="text"
                    class="form-control modern-search-input"
                    placeholder="İsim, email, telefon, şehir ile ara..."
                    [(ngModel)]="searchText"
                    (ngModelChange)="onSearchChange($event)"
                  />
                </div>
              </div>
              <div class="col-md-6 text-end">
                <div class="results-info">
                  <span class="text-muted">
                    {{ totalItems }} kullanıcıdan {{ companyUsers.length }} tanesi gösteriliyor
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Table View -->
          <div class="table-container" *ngIf="viewMode === 'table'">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      <i class="fas fa-user me-2"></i>
                      Kullanıcı
                    </th>
                    <th>
                      <i class="fas fa-envelope me-2"></i>
                      İletişim
                    </th>
                    <th>
                      <i class="fas fa-map-marker-alt me-2"></i>
                      Konum
                    </th>
                    <th>
                      <i class="fas fa-building me-2"></i>
                      Şirket
                    </th>
                    <th>
                      <i class="fas fa-info-circle me-2"></i>
                      Durum
                    </th>
                    <th class="text-center">İşlemler</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let companyUser of companyUsers" class="table-row-hover">
                    <td>
                      <div class="user-info">
                        <div class="avatar-circle" [style.backgroundColor]="getAvatarColor(companyUser.name)">
                          {{ getInitials(companyUser.name) }}
                        </div>
                        <div class="user-details">
                          <div class="user-name">{{ companyUser.name }}</div>
                          <div class="user-meta">
                            <span class="text-muted">{{ companyUser.fullDisplayName }}</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="contact-info">
                        <div class="contact-item">
                          <i class="fas fa-envelope text-muted me-2"></i>
                          <span>{{ companyUser.email }}</span>
                        </div>
                        <div class="contact-item">
                          <i class="fas fa-phone text-muted me-2"></i>
                          <span>{{ companyUser.phoneNumber }}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="location-info">
                        <div class="location-item">
                          <i class="fas fa-city text-muted me-2"></i>
                          <span>{{ companyUser.cityName }}</span>
                        </div>
                        <div class="location-item">
                          <i class="fas fa-map-pin text-muted me-2"></i>
                          <span>{{ companyUser.townName }}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="company-info" *ngIf="companyUser.companyName">
                        <div class="company-name">{{ companyUser.companyName }}</div>
                        <div class="company-phone text-muted">{{ companyUser.companyPhone }}</div>
                      </div>
                      <div *ngIf="!companyUser.companyName" class="text-muted">
                        <i class="fas fa-minus"></i> Şirket atanmamış
                      </div>
                    </td>
                    <td>
                      <span class="modern-badge" [ngClass]="getStatusClass(companyUser)">
                        {{ getStatusText(companyUser) }}
                      </span>
                    </td>
                    <td class="text-center">
                      <div class="action-buttons">
                        <button
                          class="btn-action btn-action-info"
                          (click)="viewCompanyUserDetails(companyUser)"
                          title="Detayları Görüntüle"
                        >
                          <i class="fas fa-info-circle"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Card View -->
          <div class="cards-container" *ngIf="viewMode === 'card'">
            <div class="row">
              <div class="col-xl-4 col-lg-6 col-md-6 mb-4" *ngFor="let companyUser of companyUsers">
                <div class="modern-user-card" (click)="viewCompanyUserDetails(companyUser)">
                  <div class="user-card-header">
                    <div class="avatar-circle-lg" [style.backgroundColor]="getAvatarColor(companyUser.name)">
                      {{ getInitials(companyUser.name) }}
                    </div>
                    <div class="user-card-info">
                      <h6 class="user-card-name">{{ companyUser.name }}</h6>
                      <p class="user-card-email">{{ companyUser.email }}</p>
                    </div>
                    <div class="user-card-status">
                      <span class="modern-badge" [ngClass]="getStatusClass(companyUser)">
                        {{ getStatusText(companyUser) }}
                      </span>
                    </div>
                  </div>
                  <div class="user-card-body">
                    <div class="user-card-detail">
                      <i class="fas fa-phone text-muted"></i>
                      <span>{{ companyUser.phoneNumber }}</span>
                    </div>
                    <div class="user-card-detail">
                      <i class="fas fa-map-marker-alt text-muted"></i>
                      <span>{{ companyUser.cityName }}, {{ companyUser.townName }}</span>
                    </div>
                    <div class="user-card-detail" *ngIf="companyUser.companyName">
                      <i class="fas fa-building text-muted"></i>
                      <span>{{ companyUser.companyName }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div class="pagination-container" *ngIf="totalPages > 1">
            <nav aria-label="Sayfa navigasyonu">
              <ul class="pagination modern-pagination justify-content-center">
                <li class="page-item" [class.disabled]="currentPage === 1">
                  <button class="page-link" (click)="onPageChange(currentPage - 1)" [disabled]="currentPage === 1">
                    <i class="fas fa-chevron-left"></i>
                  </button>
                </li>
                <li class="page-item" *ngFor="let page of [].constructor(totalPages); let i = index"
                    [class.active]="currentPage === i + 1">
                  <button class="page-link" (click)="onPageChange(i + 1)">{{ i + 1 }}</button>
                </li>
                <li class="page-item" [class.disabled]="currentPage === totalPages">
                  <button class="page-link" (click)="onPageChange(currentPage + 1)" [disabled]="currentPage === totalPages">
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </li>
              </ul>
            </nav>
          </div>

          <!-- Empty State -->
          <div class="empty-state" *ngIf="!isLoading && companyUsers.length === 0">
            <div class="empty-state-icon">
              <i class="fas fa-building"></i>
            </div>
            <h5>Şirket kullanıcısı bulunamadı</h5>
            <p class="text-muted">
              {{ searchText ? 'Arama kriterlerinize uygun kullanıcı bulunamadı.' : 'Henüz hiç şirket kullanıcısı eklenmemiş.' }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
